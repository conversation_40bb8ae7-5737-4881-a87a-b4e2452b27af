红颜一梦游戏文件解密工具 - 使用说明
==========================================

🎯 工具功能
-----------
这个工具可以解密"红颜一梦"游戏的加密文件，包括：
- Lua脚本文件 (.lua)
- XML配置文件 (.xml)  
- 存档文件

📁 文件位置
-----------
可执行程序：DecryptTool\bin\Release\net6.0\win-x64\publish\DecryptTool.exe
源代码：DecryptTool\ 文件夹
使用说明：使用说明.txt（本文件）

🚀 快速开始
-----------

方法一：使用命令行
1. 按 Win+R，输入 cmd，按回车打开命令行
2. 切换到游戏目录：
   cd /d "d:\QuarkNetdiskDownload\[PC]红颜赞助250625"
3. 运行解密工具：
   "DecryptTool\bin\Release\net6.0\win-x64\publish\DecryptTool.exe" "文件路径"

方法二：直接双击
1. 进入文件夹：DecryptTool\bin\Release\net6.0\win-x64\publish\
2. 双击 DecryptTool.exe
3. 按提示输入文件路径

📝 使用示例
-----------

解密单个Lua文件：
"DecryptTool\bin\Release\net6.0\win-x64\publish\DecryptTool.exe" "gamedata\suyu\HYYM\lua\main.lua"
输出：gamedata\suyu\HYYM\lua\main_decrypted.lua

批量解密lua文件夹：
"DecryptTool\bin\Release\net6.0\win-x64\publish\DecryptTool.exe" "gamedata\suyu\HYYM\lua\"
输出：所有文件都会生成对应的 _decrypted 版本

解密到指定目录：
"DecryptTool\bin\Release\net6.0\win-x64\publish\DecryptTool.exe" "gamedata\suyu\HYYM\lua\" "解密后的文件\"

查看帮助：
"DecryptTool\bin\Release\net6.0\win-x64\publish\DecryptTool.exe" --help

✅ 成功测试
-----------
工具已经成功测试了以下文件：
- main.lua (金庸群侠传X外接脚本配置文件)
- AI.lua (战斗AI扩展)
- battle.lua (战斗系统)
- skill.lua (技能系统)
- 等等...

批量测试结果：12/13 个文件成功解密

🔧 技术说明
-----------
- 工具自动检测文件加密类型
- 支持多种加密格式：数字@格式、@格式、XML存档格式
- 包含CRC16校验确保解密正确性
- 无需安装.NET运行时，可直接运行

⚠️ 注意事项
-----------
1. 建议在解密前备份原始文件
2. 未加密的文件会被自动跳过
3. 解密失败的文件不会生成输出
4. 解密后的文件使用UTF-8编码

🐛 故障排除
-----------
如果遇到问题：
1. 确保文件路径正确
2. 检查文件是否真的是加密文件
3. 查看命令行输出的错误信息
4. 尝试解密单个文件而不是批量解密

📞 技术支持
-----------
如有问题，请检查 DecryptTool/README.md 获取详细技术文档。

祝您使用愉快！ 🎉
