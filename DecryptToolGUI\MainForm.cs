using System.Text;

namespace DecryptToolGUI
{
    public partial class MainForm : Form
    {
        private Panel dropPanel;
        private Label dropLabel;
        private TextBox logTextBox;
        private Button selectFileButton;
        private Button selectFolderButton;
        private Button clearLogButton;
        private ProgressBar progressBar;
        private Label statusLabel;

        public MainForm()
        {
            InitializeComponent();
            SetupDragDrop();
        }

        private void InitializeComponent()
        {
            this.Text = "红颜一梦游戏文件解密工具";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimumSize = new Size(600, 400);
            this.Icon = SystemIcons.Application;

            // 创建主面板
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                RowCount = 4,
                ColumnCount = 1,
                Padding = new Padding(10)
            };

            // 设置行高
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 150));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 50));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30));

            // 拖拽区域
            dropPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.LightBlue,
                AllowDrop = true
            };

            dropLabel = new Label
            {
                Text = "拖拽文件或文件夹到这里进行解密\n\n支持的文件类型：\n• .lua 脚本文件\n• .xml 配置文件\n• 存档文件",
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill,
                Font = new Font("微软雅黑", 12, FontStyle.Regular),
                ForeColor = Color.DarkBlue
            };

            dropPanel.Controls.Add(dropLabel);

            // 按钮面板
            var buttonPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.LeftToRight,
                WrapContents = false,
                Padding = new Padding(0, 10, 0, 10)
            };

            selectFileButton = new Button
            {
                Text = "选择文件",
                Size = new Size(100, 30),
                Margin = new Padding(0, 0, 10, 0)
            };

            selectFolderButton = new Button
            {
                Text = "选择文件夹",
                Size = new Size(100, 30),
                Margin = new Padding(0, 0, 10, 0)
            };

            clearLogButton = new Button
            {
                Text = "清空日志",
                Size = new Size(100, 30),
                Margin = new Padding(0, 0, 10, 0)
            };

            buttonPanel.Controls.AddRange(new Control[] { selectFileButton, selectFolderButton, clearLogButton });

            // 日志区域
            logTextBox = new TextBox
            {
                Dock = DockStyle.Fill,
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                ReadOnly = true,
                Font = new Font("Consolas", 9),
                BackColor = Color.Black,
                ForeColor = Color.LimeGreen
            };

            // 状态栏
            var statusPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Height = 30
            };

            progressBar = new ProgressBar
            {
                Dock = DockStyle.Left,
                Width = 200,
                Visible = false
            };

            statusLabel = new Label
            {
                Text = "就绪",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleLeft,
                Padding = new Padding(10, 0, 0, 0)
            };

            statusPanel.Controls.AddRange(new Control[] { progressBar, statusLabel });

            // 添加到主面板
            mainPanel.Controls.Add(dropPanel, 0, 0);
            mainPanel.Controls.Add(buttonPanel, 0, 1);
            mainPanel.Controls.Add(logTextBox, 0, 2);
            mainPanel.Controls.Add(statusPanel, 0, 3);

            this.Controls.Add(mainPanel);

            // 绑定事件
            selectFileButton.Click += SelectFileButton_Click;
            selectFolderButton.Click += SelectFolderButton_Click;
            clearLogButton.Click += ClearLogButton_Click;
        }

        private void SetupDragDrop()
        {
            dropPanel.DragEnter += DropPanel_DragEnter;
            dropPanel.DragDrop += DropPanel_DragDrop;
            dropPanel.DragOver += DropPanel_DragOver;
            dropPanel.DragLeave += DropPanel_DragLeave;
        }

        private void DropPanel_DragEnter(object? sender, DragEventArgs e)
        {
            if (e.Data?.GetDataPresent(DataFormats.FileDrop) == true)
            {
                e.Effect = DragDropEffects.Copy;
                dropPanel.BackColor = Color.LightGreen;
                dropLabel.Text = "松开鼠标开始解密";
            }
        }

        private void DropPanel_DragOver(object? sender, DragEventArgs e)
        {
            if (e.Data?.GetDataPresent(DataFormats.FileDrop) == true)
            {
                e.Effect = DragDropEffects.Copy;
            }
        }

        private void DropPanel_DragLeave(object? sender, EventArgs e)
        {
            dropPanel.BackColor = Color.LightBlue;
            dropLabel.Text = "拖拽文件或文件夹到这里进行解密\n\n支持的文件类型：\n• .lua 脚本文件\n• .xml 配置文件\n• 存档文件";
        }

        private async void DropPanel_DragDrop(object? sender, DragEventArgs e)
        {
            dropPanel.BackColor = Color.LightBlue;
            dropLabel.Text = "拖拽文件或文件夹到这里进行解密\n\n支持的文件类型：\n• .lua 脚本文件\n• .xml 配置文件\n• 存档文件";

            if (e.Data?.GetData(DataFormats.FileDrop) is string[] files)
            {
                await ProcessFiles(files);
            }
        }

        private async void SelectFileButton_Click(object? sender, EventArgs e)
        {
            using var openFileDialog = new OpenFileDialog
            {
                Title = "选择要解密的文件",
                Filter = "所有支持的文件|*.lua;*.xml|Lua脚本|*.lua|XML文件|*.xml|所有文件|*.*",
                Multiselect = true
            };

            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                await ProcessFiles(openFileDialog.FileNames);
            }
        }

        private async void SelectFolderButton_Click(object? sender, EventArgs e)
        {
            using var folderBrowserDialog = new FolderBrowserDialog
            {
                Description = "选择要解密的文件夹"
            };

            if (folderBrowserDialog.ShowDialog() == DialogResult.OK)
            {
                await ProcessFiles(new[] { folderBrowserDialog.SelectedPath });
            }
        }

        private void ClearLogButton_Click(object? sender, EventArgs e)
        {
            logTextBox.Clear();
            LogMessage("日志已清空");
        }

        private async Task ProcessFiles(string[] paths)
        {
            var allFiles = new List<string>();

            // 收集所有文件
            foreach (string path in paths)
            {
                if (File.Exists(path))
                {
                    allFiles.Add(path);
                }
                else if (Directory.Exists(path))
                {
                    var files = Directory.GetFiles(path, "*.*", SearchOption.AllDirectories)
                        .Where(f => f.EndsWith(".lua", StringComparison.OrdinalIgnoreCase) ||
                                   f.EndsWith(".xml", StringComparison.OrdinalIgnoreCase) ||
                                   !Path.HasExtension(f))
                        .ToArray();
                    allFiles.AddRange(files);
                }
            }

            if (allFiles.Count == 0)
            {
                LogMessage("未找到支持的文件");
                return;
            }

            // 显示进度条
            progressBar.Visible = true;
            progressBar.Maximum = allFiles.Count;
            progressBar.Value = 0;

            int successCount = 0;
            int totalCount = allFiles.Count;

            LogMessage($"开始处理 {totalCount} 个文件...");

            foreach (string file in allFiles)
            {
                try
                {
                    statusLabel.Text = $"正在处理: {Path.GetFileName(file)}";
                    
                    bool success = await ProcessSingleFile(file);
                    if (success)
                    {
                        successCount++;
                        LogMessage($"✓ 解密成功: {Path.GetFileName(file)}");
                    }
                    else
                    {
                        LogMessage($"✗ 跳过文件: {Path.GetFileName(file)} (未加密或解密失败)");
                    }
                }
                catch (Exception ex)
                {
                    LogMessage($"✗ 解密失败: {Path.GetFileName(file)} - {ex.Message}");
                }

                progressBar.Value++;
                Application.DoEvents();
            }

            progressBar.Visible = false;
            statusLabel.Text = $"完成! 成功解密 {successCount}/{totalCount} 个文件";
            LogMessage($"批量解密完成: {successCount}/{totalCount} 个文件成功解密");
        }

        private async Task<bool> ProcessSingleFile(string filePath)
        {
            return await Task.Run(() =>
            {
                try
                {
                    string content = File.ReadAllText(filePath, Encoding.UTF8);
                    
                    if (!GameFileDecryptor.IsEncrypted(content))
                    {
                        return false; // 文件未加密
                    }

                    string decryptedContent = GameFileDecryptor.DecryptFileContent(content, filePath);
                    
                    if (decryptedContent == content)
                    {
                        return false; // 解密失败或内容未改变
                    }

                    string outputPath = GameFileDecryptor.GetOutputFileName(filePath);
                    File.WriteAllText(outputPath, decryptedContent, Encoding.UTF8);
                    
                    return true;
                }
                catch
                {
                    return false;
                }
            });
        }

        private void LogMessage(string message)
        {
            if (logTextBox.InvokeRequired)
            {
                logTextBox.Invoke(new Action<string>(LogMessage), message);
                return;
            }

            string timestamp = DateTime.Now.ToString("HH:mm:ss");
            logTextBox.AppendText($"[{timestamp}] {message}\r\n");
            logTextBox.ScrollToCaret();
        }
    }
}
