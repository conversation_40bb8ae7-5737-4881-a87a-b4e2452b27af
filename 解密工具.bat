@echo off
title Game File Decryptor

echo.
echo ==========================================
echo    Game File Decryptor Tool
echo ==========================================
echo.

if "%~1"=="" (
    echo Usage:
    echo 1. Drag files or folders to this batch file
    echo 2. Or run DecryptTool.exe directly
    echo.
    echo Examples:
    echo   Decrypt single file: drag .lua or .xml file here
    echo   Batch decrypt: drag entire folder here
    echo.
    pause
    exit /b
)

echo Decrypting: %~1
echo.

"%~dp0DecryptTool.exe" "%~1"

echo.
echo Decryption completed!
echo Press any key to exit...
pause >nul
