using System.Security.Cryptography;
using System.Text;

namespace DecryptToolGUI
{
    /// <summary>
    /// SaveManager解密算法实现
    /// 基于SaveManager.txt中的原始C#代码转换而来
    /// </summary>
    public static class SaveManagerDecryptor
    {
        /// <summary>
        /// ExtractString方法 - 解密以@开头的特殊格式字符串
        /// </summary>
        public static string ExtractString(string str)
        {
            if (string.IsNullOrEmpty(str) || !str.StartsWith("@"))
                return str;

            try
            {
                // 按照原始算法：替换字符，去掉首尾，替换特殊字符，然后Base64解码
                string processed = str.Replace('\\', '0').Replace('_', '1').Substring(1, str.Length - 2)
                    .Replace("/", "")
                    .Replace("#", "/");
                
                // Base64解码
                string decoded = Decode(Encoding.UTF8, processed);
                
                // 按特殊格式解析：每2个字符为一组，第2个字符表示第1个字符的重复次数
                StringBuilder stringBuilder = new StringBuilder();
                for (int i = 0; i < decoded.Length; i += 2)
                {
                    if (i + 1 < decoded.Length)
                    {
                        int num = (int)(decoded[i + 1] - '0');
                        for (int j = 0; j < num; j++)
                        {
                            stringBuilder.Append(decoded[i]);
                        }
                    }
                }
                
                return stringBuilder.ToString();
            }
            catch (Exception)
            {
                return str; // 解密失败时返回原字符串
            }
        }

        /// <summary>
        /// crcm方法 - 解密带CRC校验的字符串
        /// </summary>
        public static string Crcm(string input)
        {
            if (string.IsNullOrEmpty(input) || !input.Contains("@"))
                return string.Empty;

            try
            {
                // 找到第一个@的位置
                int atIndex = input.IndexOf('@');
                if (atIndex <= 0)
                    return string.Empty;

                string crcPart = input.Substring(0, atIndex);
                string encryptedPart = input.Substring(atIndex + 1);
                
                // 解密第二部分
                string decrypted = DecryptTripleDES(encryptedPart);
                
                // 计算CRC校验
                string calculatedCrc = CRC16_C(decrypted);
                
                // 验证CRC
                if (crcPart != calculatedCrc)
                {
                    return string.Empty;
                }

                return decrypted;
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// Decode_Save方法 - 解密存档字符串
        /// </summary>
        public static string DecodeSave(string str)
        {
            if (string.IsNullOrEmpty(str))
                return str;

            try
            {
                // 字符替换和Base64解码
                string processed = str.Replace('\\', '0').Replace('_', '1');
                return Decode(Encoding.UTF8, processed);
            }
            catch (Exception)
            {
                return str;
            }
        }

        /// <summary>
        /// GetDecode方法 - 解密完整存档文件
        /// </summary>
        public static string GetDecode(string input, bool flag)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            try
            {
                // 查找XML标签
                int startIndex = input.IndexOf('<');
                int endIndex = input.LastIndexOf('>');
                
                if (startIndex >= 0 && endIndex > startIndex)
                {
                    string beforeTag = input.Substring(0, startIndex);
                    string xmlTag = input.Substring(startIndex, endIndex - startIndex + 1);
                    string afterTag = input.Substring(endIndex + 1);
                    
                    // 解密标签前后的内容
                    string decryptedBefore = DecodeSave(beforeTag);
                    string decryptedAfter = DecodeSave(afterTag);
                    
                    return decryptedBefore + xmlTag + decryptedAfter;
                }
                
                return DecodeSave(input);
            }
            catch (Exception)
            {
                return input;
            }
        }

        /// <summary>
        /// Base64解码方法
        /// </summary>
        private static string Decode(Encoding encoding, string str)
        {
            try
            {
                byte[] bytes = Convert.FromBase64String(str);
                return encoding.GetString(bytes);
            }
            catch
            {
                return str;
            }
        }

        /// <summary>
        /// TripleDES解密方法
        /// </summary>
        private static string DecryptTripleDES(string encryptedText)
        {
            try
            {
                string key = "Yh$45Ct@mods";
                
                // 使用MD5哈希生成密钥
                using (var md5 = new MD5CryptoServiceProvider())
                {
                    byte[] keyBytes = md5.ComputeHash(Encoding.UTF8.GetBytes(key));
                    
                    using (var des = new TripleDESCryptoServiceProvider())
                    {
                        des.Key = keyBytes;
                        des.Mode = CipherMode.ECB;
                        des.Padding = PaddingMode.PKCS7;
                        
                        byte[] encryptedBytes = Convert.FromBase64String(encryptedText);
                        
                        using (var decryptor = des.CreateDecryptor())
                        {
                            byte[] decryptedBytes = decryptor.TransformFinalBlock(encryptedBytes, 0, encryptedBytes.Length);
                            return Encoding.UTF8.GetString(decryptedBytes);
                        }
                    }
                }
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// CRC16校验计算
        /// </summary>
        private static string CRC16_C(string input)
        {
            try
            {
                byte[] data = Encoding.UTF8.GetBytes(input);
                ushort crc = 0xFFFF;
                
                for (int i = 0; i < data.Length; i++)
                {
                    crc ^= (ushort)(data[i] << 8);
                    for (int j = 0; j < 8; j++)
                    {
                        if ((crc & 0x8000) != 0)
                        {
                            crc = (ushort)((crc << 1) ^ 0x1021);
                        }
                        else
                        {
                            crc <<= 1;
                        }
                    }
                }
                
                return (crc & 0xFFFF).ToString();
            }
            catch
            {
                return "0";
            }
        }
    }
}
