using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace DecryptTool
{
    /// <summary>
    /// SaveManager解密类，实现游戏中的各种解密算法
    /// </summary>
    public class SaveManagerDecryptor
    {
        private const string EncryptionKey = "Yh$45Ct@mods";

        /// <summary>
        /// ExtractString方法 - 解密以@开头的特殊格式字符串
        /// </summary>
        public static string ExtractString(string str)
        {
            if (string.IsNullOrEmpty(str) || !str.StartsWith("@"))
                return str;

            try
            {
                // 按照原始算法：替换字符，去掉首尾，替换特殊字符，然后Base64解码
                string processed = str.Replace('\\', '0').Replace('_', '1').Substring(1, str.Length - 2)
                    .Replace("/", "")
                    .Replace("#", "/");

                // Base64解码
                string decoded = Decode(Encoding.UTF8, processed);

                // 按特殊格式解析：每2个字符为一组，第2个字符表示第1个字符的重复次数
                StringBuilder stringBuilder = new StringBuilder();
                for (int i = 0; i < decoded.Length; i += 2)
                {
                    if (i + 1 < decoded.Length)
                    {
                        int num = (int)(decoded[i + 1] - '0');
                        for (int j = 0; j < num; j++)
                        {
                            stringBuilder.Append(decoded[i]);
                        }
                    }
                }

                return stringBuilder.ToString();
            }
            catch (Exception)
            {
                return str; // 解密失败时返回原字符串
            }
        }

        /// <summary>
        /// crcm方法 - 解密带CRC校验的字符串
        /// </summary>
        public static string Crcm(string input)
        {
            if (string.IsNullOrEmpty(input) || !input.Contains("@"))
                return string.Empty;

            try
            {
                // 找到第一个@的位置
                int atIndex = input.IndexOf('@');
                if (atIndex <= 0)
                    return string.Empty;

                string crcPart = input.Substring(0, atIndex);
                string encryptedPart = input.Substring(atIndex + 1);

                // 解密第二部分
                string decrypted = DecryptTripleDES(encryptedPart);

                // 计算CRC校验
                string calculatedCrc = CRC16_C(decrypted);

                // 验证CRC
                if (crcPart != calculatedCrc)
                {
                    return string.Empty;
                }

                return decrypted;
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// Decode_Save方法 - 解密存档文件内容
        /// </summary>
        public static string DecodeSave(string result)
        {
            if (string.IsNullOrEmpty(result))
                return string.Empty;

            try
            {
                // 处理末尾的'@'字符
                if (result.EndsWith("@"))
                {
                    result = result.Substring(0, result.Length - 1) + "=";
                }
                
                // 去掉首字符
                result = result.Substring(1, result.Length - 1);
                
                // 替换：'#' → '', '$' → '/'
                result = result.Replace("#", "").Replace("$", "/");
                
                // 使用getResult方法转换
                result = GetResult(result);
                
                // Base64解码
                return Decode(Encoding.UTF8, result);
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// GetDecode方法 - 解密完整的存档文件
        /// </summary>
        public static string GetDecode(string content, bool forList = false)
        {
            if (string.IsNullOrEmpty(content))
                return string.Empty;

            try
            {
                string startTag = string.Empty;
                int startIndex = content.IndexOf('>');
                if (startIndex != -1)
                {
                    startTag = content.Substring(0, startIndex + 1);
                }

                string endTag = string.Empty;
                if (forList && startTag.IndexOf("information=") > 22)
                {
                    return string.Empty;
                }

                int endIndex = content.LastIndexOf('<');
                if (endIndex != -1)
                {
                    endTag = content.Substring(endIndex, content.Length - endIndex);
                }

                if (startIndex != -1 && endIndex != -1)
                {
                    content = content.Substring(startIndex + 1, endIndex - startIndex - 1);
                }

                content = DecodeSave(content);
                return startTag + content + endTag;
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// TripleDES解密
        /// </summary>
        private static string DecryptTripleDES(string encryptedText)
        {
            UTF8Encoding utf8Encoding = new UTF8Encoding();
            MD5CryptoServiceProvider md5Provider = new MD5CryptoServiceProvider();
            byte[] keyBytes = md5Provider.ComputeHash(utf8Encoding.GetBytes(EncryptionKey));
            
            TripleDESCryptoServiceProvider tripleDesProvider = new TripleDESCryptoServiceProvider
            {
                Key = keyBytes,
                Mode = CipherMode.ECB,
                Padding = PaddingMode.PKCS7
            };

            byte[] encryptedBytes = Convert.FromBase64String(encryptedText);
            byte[] decryptedBytes;
            
            try
            {
                decryptedBytes = tripleDesProvider.CreateDecryptor().TransformFinalBlock(encryptedBytes, 0, encryptedBytes.Length);
            }
            finally
            {
                tripleDesProvider.Clear();
                md5Provider.Clear();
            }
            
            return utf8Encoding.GetString(decryptedBytes);
        }

        /// <summary>
        /// CRC16校验计算
        /// </summary>
        private static string CRC16_C(string str)
        {
            byte[] bytes = Encoding.UTF8.GetBytes(str);
            byte crcHigh = 255;
            byte crcLow = 255;
            byte poly1 = 1;
            byte poly2 = 160;
            
            foreach (byte b in bytes)
            {
                crcHigh ^= b;
                for (int i = 0; i <= 7; i++)
                {
                    byte tempHigh = crcLow;
                    byte tempLow = crcHigh;
                    crcLow = (byte)(crcLow >> 1);
                    crcHigh = (byte)(crcHigh >> 1);
                    
                    if ((tempHigh & 1) == 1)
                    {
                        crcHigh |= 128;
                    }
                    
                    if ((tempLow & 1) == 1)
                    {
                        crcLow ^= poly2;
                        crcHigh ^= poly1;
                    }
                }
            }
            
            return string.Format("{0}{1}", crcLow, crcHigh);
        }

        /// <summary>
        /// Base64解码
        /// </summary>
        private static string Decode(Encoding encoding, string result)
        {
            try
            {
                byte[] bytes = Convert.FromBase64String(result);
                return encoding.GetString(bytes);
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// 字符转换方法
        /// </summary>
        private static string GetResult(string input)
        {
            char[] upperChars = {
                'Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P',
                'A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L', 'Z',
                'X', 'C', 'V', 'B', 'N', 'M'
            };
            
            char[] lowerChars = {
                'q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p',
                'a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', 'z',
                'x', 'c', 'v', 'b', 'n', 'm'
            };

            StringBuilder result = new StringBuilder();
            
            foreach (char c in input)
            {
                int upperIndex = Array.IndexOf(upperChars, c);
                if (upperIndex != -1)
                {
                    result.Append(lowerChars[upperIndex]);
                }
                else
                {
                    int lowerIndex = Array.IndexOf(lowerChars, c);
                    if (lowerIndex != -1)
                    {
                        result.Append(upperChars[lowerIndex]);
                    }
                    else
                    {
                        result.Append(c);
                    }
                }
            }
            
            return result.ToString();
        }
    }
}
