{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {}, ".NETCoreApp,Version=v6.0/win-x64": {"DecryptTool/1.0.0": {"dependencies": {"Microsoft.NET.ILLink.Analyzers": "7.0.100-1.23211.1", "Microsoft.NET.ILLink.Tasks": "7.0.100-1.23211.1", "runtimepack.Microsoft.NETCore.App.Runtime.win-x64": "6.0.36"}, "runtime": {"DecryptTool.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/6.0.36": {"runtime": {"System.Collections.Concurrent.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.3624.51421"}, "System.Collections.Immutable.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.3624.51421"}, "System.Console.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.3624.51421"}, "System.IO.Compression.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.3624.51421"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.3624.51421"}, "System.Private.CoreLib.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.3624.51421"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.3624.51421"}, "System.Runtime.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.3624.51421"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.3624.51421"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.3624.51421"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.3624.51421"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.3624.51421"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.3624.51421"}}}, "Microsoft.NET.ILLink.Analyzers/7.0.100-1.23211.1": {}, "Microsoft.NET.ILLink.Tasks/7.0.100-1.23211.1": {}}}, "libraries": {"DecryptTool/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/6.0.36": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "Microsoft.NET.ILLink.Analyzers/7.0.100-1.23211.1": {"type": "package", "serviceable": true, "sha512": "sha512-0GvbEgDGcUQA9KuWcQU1WwYHXt1tBzNr1Nls/M57rM7NA/AndFwCaCEoJpJkmxRY7xLlPDBnmGp8h5+FNqUngg==", "path": "microsoft.net.illink.analyzers/7.0.100-1.23211.1", "hashPath": "microsoft.net.illink.analyzers.7.0.100-1.23211.1.nupkg.sha512"}, "Microsoft.NET.ILLink.Tasks/7.0.100-1.23211.1": {"type": "package", "serviceable": true, "sha512": "sha512-tvG8XZYLjT0o3WicCyKBZysVWo1jC9HdCFmNRmddx3WbAz0UCsd0qKZqpiEo99VLA8Re+FzWK51OcRldQPbt2Q==", "path": "microsoft.net.illink.tasks/7.0.100-1.23211.1", "hashPath": "microsoft.net.illink.tasks.7.0.100-1.23211.1.nupkg.sha512"}}, "runtimes": {"win-x64": ["win", "any", "base"], "win-x64-aot": ["win-aot", "win-x64", "win", "aot", "any", "base"], "win10-x64": ["win10", "win81-x64", "win81", "win8-x64", "win8", "win7-x64", "win7", "win-x64", "win", "any", "base"], "win10-x64-aot": ["win10-aot", "win10-x64", "win10", "win81-x64-aot", "win81-aot", "win81-x64", "win81", "win8-x64-aot", "win8-aot", "win8-x64", "win8", "win7-x64-aot", "win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"], "win7-x64": ["win7", "win-x64", "win", "any", "base"], "win7-x64-aot": ["win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"], "win8-x64": ["win8", "win7-x64", "win7", "win-x64", "win", "any", "base"], "win8-x64-aot": ["win8-aot", "win8-x64", "win8", "win7-x64-aot", "win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"], "win81-x64": ["win81", "win8-x64", "win8", "win7-x64", "win7", "win-x64", "win", "any", "base"], "win81-x64-aot": ["win81-aot", "win81-x64", "win81", "win8-x64-aot", "win8-aot", "win8-x64", "win8", "win7-x64-aot", "win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"]}}