using System.Text.RegularExpressions;

namespace DecryptToolGUI
{
    /// <summary>
    /// 游戏文件解密器 - 处理不同类型的加密文件
    /// </summary>
    public static class GameFileDecryptor
    {
        /// <summary>
        /// 解密文件内容
        /// </summary>
        public static string DecryptFileContent(string content, string fileName)
        {
            if (string.IsNullOrEmpty(content))
                return content;

            try
            {
                // 检测文件类型并选择相应的解密方法
                if (fileName.EndsWith(".lua", StringComparison.OrdinalIgnoreCase))
                {
                    return DecryptLuaFile(content);
                }
                else if (fileName.EndsWith(".xml", StringComparison.OrdinalIgnoreCase))
                {
                    return DecryptXmlFile(content);
                }
                else
                {
                    // 尝试作为存档文件解密
                    return DecryptSaveFile(content);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"解密文件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 解密Lua文件
        /// </summary>
        private static string DecryptLuaFile(string content)
        {
            return DecryptEncryptedFormat(content);
        }

        /// <summary>
        /// 解密XML文件
        /// </summary>
        private static string DecryptXmlFile(string content)
        {
            return DecryptEncryptedFormat(content);
        }

        /// <summary>
        /// 解密存档文件
        /// </summary>
        private static string DecryptSaveFile(string content)
        {
            // 尝试GetDecode方法
            string result = SaveManagerDecryptor.GetDecode(content, true);
            if (result != content)
                return result;

            // 尝试DecodeSave方法
            result = SaveManagerDecryptor.DecodeSave(content);
            if (result != content)
                return result;

            // 尝试加密格式解密
            return DecryptEncryptedFormat(content);
        }

        /// <summary>
        /// 解密加密格式的内容（数字@格式或@格式）
        /// </summary>
        private static string DecryptEncryptedFormat(string content)
        {
            if (string.IsNullOrEmpty(content))
                return content;

            // 检测是否为数字@格式（如：12022@...）
            var numberAtMatch = Regex.Match(content, @"^(\d+)@(.+)$", RegexOptions.Singleline);
            if (numberAtMatch.Success)
            {
                // 使用crcm方法解密
                string decrypted = SaveManagerDecryptor.Crcm(content);
                if (!string.IsNullOrEmpty(decrypted))
                    return decrypted;
            }

            // 检测是否为@格式
            if (content.StartsWith("@"))
            {
                // 使用ExtractString方法解密
                string decrypted = SaveManagerDecryptor.ExtractString(content);
                if (decrypted != content)
                    return decrypted;
            }

            // 如果都不匹配，返回原内容
            return content;
        }

        /// <summary>
        /// 检查内容是否已加密
        /// </summary>
        public static bool IsEncrypted(string content)
        {
            if (string.IsNullOrEmpty(content))
                return false;

            // 检查是否为已知的加密格式
            return content.StartsWith("@") || 
                   Regex.IsMatch(content, @"^\d+@", RegexOptions.Singleline) ||
                   content.Contains("\\") && content.Contains("_");
        }

        /// <summary>
        /// 获取输出文件名
        /// </summary>
        public static string GetOutputFileName(string inputFileName)
        {
            string directory = Path.GetDirectoryName(inputFileName) ?? "";
            string nameWithoutExt = Path.GetFileNameWithoutExtension(inputFileName);
            string extension = Path.GetExtension(inputFileName);
            
            return Path.Combine(directory, $"{nameWithoutExt}_decrypted{extension}");
        }
    }
}
