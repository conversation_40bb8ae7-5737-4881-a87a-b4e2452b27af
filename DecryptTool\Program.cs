using System;
using System.IO;

namespace DecryptTool
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== 游戏文件解密工具 ===");
            Console.WriteLine("支持解密 Lua 脚本、XML 文件和存档文件");
            Console.WriteLine();

            if (args.Length == 0)
            {
                ShowUsage();
                return;
            }

            try
            {
                ProcessArguments(args);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"错误: {ex.Message}");
                Environment.Exit(1);
            }
        }

        static void ShowUsage()
        {
            Console.WriteLine("用法:");
            Console.WriteLine("  DecryptTool.exe <文件路径> [输出路径]           - 解密单个文件");
            Console.WriteLine("  DecryptTool.exe <文件夹路径> [输出文件夹]       - 批量解密文件夹中的所有文件");
            Console.WriteLine("  DecryptTool.exe -h, --help                    - 显示帮助信息");
            Console.WriteLine();
            Console.WriteLine("支持的文件类型:");
            Console.WriteLine("  .lua  - Lua 脚本文件");
            Console.WriteLine("  .xml  - XML 配置文件");
            Console.WriteLine("  存档文件 (无扩展名或其他扩展名)");
            Console.WriteLine();
            Console.WriteLine("示例:");
            Console.WriteLine("  DecryptTool.exe main.lua");
            Console.WriteLine("  DecryptTool.exe Scripts/");
            Console.WriteLine("  DecryptTool.exe gamedata/suyu/HYYM/");
        }

        static void ProcessArguments(string[] args)
        {
            string path = args[0];

            if (path == "-h" || path == "--help")
            {
                ShowUsage();
                return;
            }

            // 检查是否指定了输出路径
            string? outputPath = null;
            if (args.Length > 1)
            {
                outputPath = args[1];
            }

            if (File.Exists(path))
            {
                // 处理单个文件
                DecryptFile(path, outputPath);
            }
            else if (Directory.Exists(path))
            {
                // 处理文件夹
                DecryptDirectory(path, outputPath);
            }
            else
            {
                throw new FileNotFoundException($"文件或文件夹不存在: {path}");
            }
        }

        static void DecryptFile(string filePath, string? outputPath = null)
        {
            Console.WriteLine($"正在解密文件: {filePath}");

            var decryptor = new GameFileDecryptor();
            outputPath ??= GenerateOutputPath(filePath);

            if (decryptor.DecryptFile(filePath, outputPath))
            {
                Console.WriteLine($"解密成功: {outputPath}");
            }
            else
            {
                Console.WriteLine($"解密失败或文件未加密: {filePath}");
            }
        }

        static void DecryptDirectory(string directoryPath, string? outputPath = null)
        {
            Console.WriteLine($"正在批量解密文件夹: {directoryPath}");

            var decryptor = new GameFileDecryptor();

            if (!string.IsNullOrEmpty(outputPath))
            {
                // 批量解密到指定目录
                decryptor.DecryptDirectory(directoryPath, outputPath);
            }
            else
            {
                // 在原地解密
                int successCount = 0;
                int totalCount = 0;

                foreach (string filePath in Directory.GetFiles(directoryPath, "*", SearchOption.AllDirectories))
                {
                    totalCount++;
                    string generatedOutputPath = GenerateOutputPath(filePath);

                    Console.Write($"处理: {Path.GetFileName(filePath)}... ");

                    if (decryptor.DecryptFile(filePath, generatedOutputPath))
                    {
                        Console.WriteLine("成功");
                        successCount++;
                    }
                    else
                    {
                        Console.WriteLine("跳过");
                    }
                }

                Console.WriteLine();
                Console.WriteLine($"批量解密完成: {successCount}/{totalCount} 个文件成功解密");
            }
        }

        static string GenerateOutputPath(string inputPath)
        {
            string directory = Path.GetDirectoryName(inputPath) ?? "";
            string fileName = Path.GetFileNameWithoutExtension(inputPath);
            string extension = Path.GetExtension(inputPath);
            
            return Path.Combine(directory, $"{fileName}_decrypted{extension}");
        }
    }
}
