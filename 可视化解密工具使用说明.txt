🎯 红颜一梦游戏文件解密工具 - 可视化版本
==========================================

📁 程序文件
-----------
主程序：红颜一梦解密工具.exe
源代码：DecryptToolGUI\ 文件夹

🚀 使用方法
-----------

方法一：拖拽操作（推荐）
1. 双击运行 "红颜一梦解密工具.exe"
2. 将要解密的文件或文件夹直接拖拽到蓝色区域
3. 程序会自动开始解密，并显示进度

方法二：按钮选择
1. 双击运行 "红颜一梦解密工具.exe"
2. 点击"选择文件"按钮选择单个文件
3. 或点击"选择文件夹"按钮选择整个文件夹
4. 程序会自动开始解密

✨ 功能特点
-----------
• 🖱️ 支持拖拽操作，使用简单
• 📊 实时显示解密进度
• 📝 详细的操作日志
• 🔍 自动识别加密文件类型
• 📁 支持批量解密整个文件夹
• ✅ 显示解密成功/失败状态

📋 支持的文件类型
-----------------
• .lua 脚本文件
• .xml 配置文件  
• 存档文件（无扩展名）

📝 使用示例
-----------
1. 解密单个文件：
   - 拖拽 main.lua 到程序窗口
   - 生成 main_decrypted.lua

2. 批量解密：
   - 拖拽 gamedata\suyu\HYYM\lua\ 文件夹到程序窗口
   - 自动解密文件夹内所有支持的文件

3. 查看日志：
   - 程序下方会显示详细的解密日志
   - 绿色文字显示成功，红色显示失败

🎨 界面说明
-----------
• 蓝色拖拽区域：将文件或文件夹拖拽到这里
• 按钮区域：选择文件、选择文件夹、清空日志
• 日志区域：显示解密过程和结果
• 状态栏：显示当前操作状态和进度条

✅ 已验证功能
-----------
• ✅ 图形界面：美观易用的Windows窗体界面
• ✅ 拖拽支持：直接拖拽文件进行解密
• ✅ 批量处理：一次性解密整个文件夹
• ✅ 进度显示：实时显示解密进度
• ✅ 日志记录：详细记录每个文件的处理结果
• ✅ 多种格式：支持@格式和数字@格式解密
• ✅ 独立运行：无需安装.NET运行时

⚠️ 注意事项
-----------
1. 程序会自动跳过未加密的文件
2. 解密后的文件会添加"_decrypted"后缀
3. 建议在解密前备份原始文件
4. 如果程序无法启动，请确保系统支持.NET 6.0

🐛 故障排除
-----------
• 如果拖拽无效：尝试使用按钮选择文件
• 如果解密失败：检查文件是否真的是加密文件
• 如果程序崩溃：查看日志区域的错误信息

现在您可以享受可视化的解密体验了！🎉

只需双击运行程序，然后拖拽文件即可开始解密！
