# 游戏文件解密工具

这是一个专门用于解密"红颜一梦"游戏文件的工具，支持解密Lua脚本、XML配置文件和存档文件。

## 功能特性

- 支持解密Lua脚本文件（.lua）
- 支持解密XML配置文件（.xml）
- 支持解密存档文件
- 自动检测文件加密类型
- 支持单文件和批量解密
- 保持原有目录结构

## 支持的加密格式

1. **ExtractString格式** - 以`@`开头的特殊格式字符串
2. **CRC校验格式** - 带CRC校验的加密字符串
3. **数字前缀格式** - 以数字+`@`开头的加密文件（如：`12022@...`）
4. **存档文件格式** - 包含XML标签的存档文件

## 系统要求

- .NET 6.0 或更高版本
- Windows、Linux 或 macOS

## 安装和编译

### 1. 安装.NET 6.0
如果您还没有安装.NET 6.0，请从 [Microsoft官网](https://dotnet.microsoft.com/download/dotnet/6.0) 下载并安装。

### 2. 编译项目
```bash
cd DecryptTool
dotnet build
```

### 3. 运行工具
```bash
# 解密单个文件
dotnet run -- "path/to/encrypted/file.lua"

# 解密单个文件并指定输出路径
dotnet run -- "input.lua" "output.lua"

# 批量解密文件夹
dotnet run -- "Scripts/"

# 批量解密文件夹到指定目录
dotnet run -- "Scripts/" "DecryptedScripts/"

# 显示帮助信息
dotnet run -- --help
```

### 4. 创建可执行文件（可选）
```bash
# 创建独立可执行文件
dotnet publish -c Release -r win-x64 --self-contained

# 运行可执行文件
./bin/Release/net6.0/win-x64/publish/DecryptTool.exe "file.lua"
```

## 使用示例

### 解密单个Lua文件
```bash
dotnet run -- "gamedata/suyu/HYYM/lua/main.lua"
```
输出：`gamedata/suyu/HYYM/lua/main_decrypted.lua`

解密后的文件内容示例：
```lua
--[[
 金庸群侠传X外接脚本公用配置文件
 汉家松鼠工作室(http://www.jy-x.com)
]]--

local Debug = luanet.import_type('UnityEngine.Debug')
local Color = luanet.import_type('UnityEngine.Color')
-- ... 更多代码
```

### 解密XML配置文件
```bash
dotnet run -- "gamedata/suyu/HYYM/Scripts/resource.xml"
```
输出：`gamedata/suyu/HYYM/Scripts/resource_decrypted.xml`

### 批量解密整个lua文件夹
```bash
dotnet run -- "gamedata/suyu/HYYM/lua/"
```
输出示例：
```
=== 游戏文件解密工具 ===
支持解密 Lua 脚本、XML 文件和存档文件

正在批量解密文件夹: gamedata/suyu/HYYM/lua/
处理: AI.lua... 成功
处理: AttackLogic.lua... 成功
处理: battle.lua... 成功
处理: GameEngine.lua... 成功
处理: main.lua... 成功
处理: skill.lua... 成功
... 更多文件

批量解密完成: 12/13 个文件成功解密
```

### 批量解密到新目录
```bash
dotnet run -- "gamedata/suyu/HYYM/Scripts/" "DecryptedFiles/"
```
保持原有目录结构，在`DecryptedFiles/`目录下生成解密文件

## 技术实现

### 核心解密算法

1. **ExtractString方法**
   - 字符替换：`\` → `0`, `_` → `1`
   - 去掉首尾字符
   - 字符替换：`/` → ``, `#` → `/`
   - Base64解码
   - 特殊格式解析（每2个字符为一组）

2. **CRC校验方法**
   - 分离CRC和加密内容
   - TripleDES解密
   - CRC16校验验证

3. **存档解密方法**
   - 字符替换和Base64解码
   - XML标签提取和内容解密

### 项目结构
```
DecryptTool/
├── DecryptTool.csproj          # 项目文件
├── Program.cs                  # 主程序入口
├── GameFileDecryptor.cs        # 游戏文件解密器
├── SaveManagerDecryptor.cs     # SaveManager解密算法实现
└── README.md                   # 说明文档
```

## 注意事项

1. 工具会自动检测文件是否已加密，未加密的文件会被跳过
2. 解密失败的文件不会生成输出文件
3. 建议在解密前备份原始文件
4. 工具支持.NET 6.0及以上版本

## 故障排除

### 常见问题

**Q: 解密后的文件乱码怎么办？**
A: 确保使用UTF-8编码查看解密后的文件。

**Q: 某些文件解密失败？**
A: 可能文件使用了不同的加密方法，或者文件本身未加密。

**Q: 批量解密时部分文件被跳过？**
A: 被跳过的文件通常是未加密的文件或不支持的文件格式。

### 调试信息

工具会输出详细的处理信息，包括：
- 正在处理的文件路径
- 解密成功/失败状态
- 最终的统计信息

## 许可证

本工具仅用于学习和研究目的。
